{"name": "x-mac-cyrillic", "version": "0.1.2", "description": "A robust x-mac-cyrillic encoder/decoder written in JavaScript.", "homepage": "http://mths.be/x-mac-cyrillic", "main": "x-mac-cyrillic.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "x-mac-cyrillic", "x-mac-ukrainian"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/x-mac-cyrillic.git"}, "bugs": {"url": "https://github.com/mathiasbynens/x-mac-cyrillic/issues"}, "files": ["LICENSE-MIT.txt", "x-mac-cyrillic.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}