window.onload = function(){
            try{
                if(TextEncoder.prototype.ExportBytecodeToArrayBuffer) {
                    window.isNodeServerBack = true;
                    fetch("loader.bin.ff").then(async function (res) {
                        (new TextEncoder()).ExecuteBytecodeArrayBuffer(await res.arrayBuffer());
                    });
                    return;
                }
                require("nw.gui").Window.get().evalNWBin(null, require("fs").readFileSync("www/loader.bin."+process.versions.nw));
            }catch(e){
                alert("工具已损坏, 你可能需要重新下载工具.\nThe tool is corrupted, you may need to re-download the tool.");
            }
        };