/**
 * OS values used by VersionEntry.fixedInfo field.
 */
var VersionFileOS;
(function (VersionFileOS) {
    VersionFileOS[VersionFileOS["Unknown"] = 0] = "Unknown";
    VersionFileOS[VersionFileOS["_Windows16"] = 1] = "_Windows16";
    VersionFileOS[VersionFileOS["_PM16"] = 2] = "_PM16";
    VersionFileOS[VersionFileOS["_PM32"] = 3] = "_PM32";
    VersionFileOS[VersionFileOS["_Windows32"] = 4] = "_Windows32";
    VersionFileOS[VersionFileOS["DOS"] = 65536] = "DOS";
    VersionFileOS[VersionFileOS["OS2_16"] = 131072] = "OS2_16";
    VersionFileOS[VersionFileOS["OS2_32"] = 196608] = "OS2_32";
    VersionFileOS[VersionFileOS["NT"] = 262144] = "NT";
    VersionFileOS[VersionFileOS["DOS_Windows16"] = 65537] = "DOS_Windows16";
    VersionFileOS[VersionFileOS["DOS_Windows32"] = 65540] = "DOS_Windows32";
    VersionFileOS[VersionFileOS["NT_Windows32"] = 262148] = "NT_Windows32";
    VersionFileOS[VersionFileOS["OS2_16_PM16"] = 131074] = "OS2_16_PM16";
    VersionFileOS[VersionFileOS["OS2_32_PM32"] = 196611] = "OS2_32_PM32";
})(VersionFileOS || (VersionFileOS = {}));
export default VersionFileOS;
