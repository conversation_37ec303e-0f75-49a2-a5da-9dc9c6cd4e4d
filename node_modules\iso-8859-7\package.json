{"name": "iso-8859-7", "version": "0.1.2", "description": "A robust iso-8859-7 encoder/decoder written in JavaScript.", "homepage": "http://mths.be/iso-8859-7", "main": "iso-8859-7.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "csisolatingreek", "ecma-118", "elot_928", "greek", "greek8", "iso-8859-7", "iso-ir-126", "iso8859-7", "iso88597", "iso_8859-7", "iso_8859-7:1987", "sun_eu_greek"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/iso-8859-7.git"}, "bugs": {"url": "https://github.com/mathiasbynens/iso-8859-7/issues"}, "files": ["LICENSE-MIT.txt", "iso-8859-7.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}