import FormatBase from './FormatBase';
import ImageFileHeader from './ImageFileHeader';
import ImageOptionalHeader from './ImageOptionalHeader';
import <PERSON>Option<PERSON><PERSON>eader64 from './ImageOptionalHeader64';
import ImageDataDirectoryArray from './ImageDataDirectoryArray';
export default class ImageNtHeaders extends FormatBase {
    static readonly DEFAULT_SIGNATURE = 17744;
    private constructor();
    static from(bin: ArrayBuffer | ArrayBufferView, offset?: number): ImageNtHeaders;
    isValid(): boolean;
    is32bit(): boolean;
    get signature(): number;
    set signature(val: number);
    get fileHeader(): ImageFileHeader;
    get optionalHeader(): ImageOptionalHeader | ImageOptionalHeader64;
    get optionalHeaderDataDirectory(): ImageDataDirectoryArray;
    getDataDirectoryOffset(): number;
    getSectionHeaderOffset(): number;
}
