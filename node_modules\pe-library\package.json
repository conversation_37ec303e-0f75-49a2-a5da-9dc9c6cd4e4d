{"name": "pe-library", "version": "0.3.0", "engines": {"node": ">=12", "npm": ">=6"}, "engineStrict": true, "description": "Node.js library for Portable Executable format", "main": "./dist/index.js", "module": "./dist/_esm/index.js", "exports": {"import": "./dist/_esm/index.js", "require": "./dist/index.js"}, "types": "./dist/index.d.ts", "author": "jet", "license": "MIT", "homepage": "https://github.com/jet2jet/pe-library-js", "keywords": ["javascript", "library", "pe", "pe-executable", "portable-executable", "exe"], "repository": {"type": "git", "url": "https://github.com/jet2jet/pe-library-js.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p ./tsconfig.app.json", "build:esm": "tsc -p ./tsconfig.app.esm.json", "lint": "npm run lint:prettier && npm run lint:eslint", "lint:eslint": "eslint -c .eslintrc.yml --ext .js,.jsx,.ts,.tsx .", "lint:eslint:fix": "eslint -c .eslintrc.yml --fix --ext .js,.jsx,.ts,.tsx .", "lint:fix": "npm run lint:prettier:fix && npm run lint:eslint:fix", "lint:prettier": "prettier --config ./.prettierrc.yml --check \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "lint:prettier:fix": "prettier --config ./.prettierrc.yml --write \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "test": "jest --config ./jest.config.basic.js", "version": "node ./tools/updateVersion.js ./src/main/version.ts && git add -A ./src/main/version.ts"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.22", "@types/node": "^12.20.37", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "eslint": "^7.24.0", "eslint-config-prettier": "^8.2.0", "eslint-config-standard": "^16.0.2", "eslint-config-standard-with-typescript": "^20.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.5.5", "typescript": "~4.2.4"}}