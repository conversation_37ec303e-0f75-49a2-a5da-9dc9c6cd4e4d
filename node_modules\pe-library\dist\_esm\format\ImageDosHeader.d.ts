import FormatBase from './FormatBase';
export default class ImageDosHeader extends FormatBase {
    static readonly size = 64;
    static readonly DEFAULT_MAGIC = 23117;
    private constructor();
    static from(bin: ArrayBuffer | ArrayBufferView, offset?: number): ImageDosHeader;
    isValid(): boolean;
    get magic(): number;
    set magic(val: number);
    get lastPageSize(): number;
    set lastPageSize(val: number);
    get pages(): number;
    set pages(val: number);
    get relocations(): number;
    set relocations(val: number);
    get headerSizeInParagraph(): number;
    set headerSizeInParagraph(val: number);
    get minAllocParagraphs(): number;
    set minAllocParagraphs(val: number);
    get maxAllocParagraphs(): number;
    set maxAllocParagraphs(val: number);
    get initialSS(): number;
    set initialSS(val: number);
    get initialSP(): number;
    set initialSP(val: number);
    get checkSum(): number;
    set checkSum(val: number);
    get initialIP(): number;
    set initialIP(val: number);
    get initialCS(): number;
    set initialCS(val: number);
    get relocationTableAddress(): number;
    set relocationTableAddress(val: number);
    get overlayNum(): number;
    set overlayNum(val: number);
    get oemId(): number;
    set oemId(val: number);
    get oemInfo(): number;
    set oemInfo(val: number);
    get newHeaderAddress(): number;
    set newHeaderAddress(val: number);
}
