{"_from": "ws", "_id": "ws@7.5.2", "_inBundle": false, "_integrity": "sha512-lkF7AWRicoB9mAgjeKbGqVUekLnSNO4VjKVnuPHpQeOxZOErX6BPXwJk70nFslRCEEA8EVW7ZjKwXaP9N+1sKQ==", "_location": "/ws", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "ws", "name": "ws", "escapedName": "ws", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/ws/-/ws-7.5.2.tgz", "_shasum": "09cc8fea3bec1bc5ed44ef51b42f945be36900f6", "_spec": "ws", "_where": "C:\\Users\\<USER>\\Documents\\rpgProjs\\nwProject", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "browser": "browser.js", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^7.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1", "mocha": "^7.0.0", "nyc": "^15.0.0", "prettier": "^2.0.5", "utf-8-validate": "^5.0.2"}, "engines": {"node": ">=8.3.0"}, "files": ["browser.js", "index.js", "lib/*.js"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js"}, "version": "7.5.2"}