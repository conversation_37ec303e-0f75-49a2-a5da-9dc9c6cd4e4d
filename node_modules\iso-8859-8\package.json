{"name": "iso-8859-8", "version": "0.1.2", "description": "A robust iso-8859-8 encoder/decoder written in JavaScript.", "homepage": "http://mths.be/iso-8859-8", "main": "iso-8859-8.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "csiso88598e", "csisolatinhebrew", "hebrew", "iso-8859-8", "iso-8859-8-e", "iso-ir-138", "iso8859-8", "iso88598", "iso_8859-8", "iso_8859-8:1988", "visual"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/iso-8859-8.git"}, "bugs": {"url": "https://github.com/mathiasbynens/iso-8859-8/issues"}, "files": ["LICENSE-MIT.txt", "iso-8859-8.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}