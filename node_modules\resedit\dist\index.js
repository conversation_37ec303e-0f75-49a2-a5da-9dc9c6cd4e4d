"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateExecutableWithSign = exports.Resource = exports.Format = exports.Data = exports.version = exports.NtExecutableResource = exports.NtExecutable = void 0;
var pe_library_1 = require("pe-library");
Object.defineProperty(exports, "NtExecutable", { enumerable: true, get: function () { return pe_library_1.NtExecutable; } });
Object.defineProperty(exports, "NtExecutableResource", { enumerable: true, get: function () { return pe_library_1.NtExecutableResource; } });
Object.defineProperty(exports, "Format", { enumerable: true, get: function () { return pe_library_1.Format; } });
var version_1 = require("./version");
exports.version = version_1.default;
var Data = require("./data");
exports.Data = Data;
var Resource = require("./resource");
exports.Resource = Resource;
var sign_1 = require("./sign");
Object.defineProperty(exports, "generateExecutableWithSign", { enumerable: true, get: function () { return sign_1.generateExecutableWithSign; } });
