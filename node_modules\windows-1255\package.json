{"name": "windows-1255", "version": "0.1.2", "description": "A robust windows-1255 encoder/decoder written in JavaScript.", "homepage": "http://mths.be/windows-1255", "main": "windows-1255.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "cp1255", "windows-1255", "x-cp1255"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/windows-1255.git"}, "bugs": {"url": "https://github.com/mathiasbynens/windows-1255/issues"}, "files": ["LICENSE-MIT.txt", "windows-1255.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}