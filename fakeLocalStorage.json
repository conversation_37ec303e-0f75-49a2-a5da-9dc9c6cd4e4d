{"tEnvOpts_fontSizeVx": "-3", "tEnvOpts_fontSizeKRKR2": "-3", "tEnvOpts_fontSizeWolf": "0", "tEnvOpts_fontSizeSRPG": "-3", "tEnvOpts_fontSizeGI": "-3", "tEnvOpts_fontSizeAGTK": "-3", "tEnvOpts_krkr_dontReloadCurrentScript": "1", "dontAlertMe": "0", "evadeAntivirus": "1", "AutoSaveBasePath": "gameSaveBackup", "lastCollCheck": "1754777642270", "tyranoFontSizeOff": "-3", "bakinFontSizeOff": "-3", "kmyFontSizeOff": "-3", "mvFixFontSize": "-3", "fontSelectVersion": "3", "translateNameFix": "0", "readAnno": "[27,26,25,15]", "bgConf": "{\"enable\":true,\"isDef\":true,\"url\":[\"https://trs.mtool.app/defbgs/def.jpg\"],\"bgOpacity\":[0.2],\"bgBlur\":[10],\"bgParallaxIntensity\":[0.1],\"performanceMode\":[true],\"overrideColorPalette\":true,\"colorPalette\":[[5,128,177],[220,215,218],[21,13,52],[93,197,225],[54,31,98]],\"againstToEdge\":[0],\"fitWithEdge\":[0],\"reverseMove\":[false],\"autoOpacityBlur\":[true],\"pixelated\":[false],\"offset\":[[0,0]]}", "defBgConfCache": "{\r\n  \"enable\": true,\r\n  \"isDef\": false,\r\n  \"url\": [\r\n    \"https://trs.mtool.app/defbgs/def.jpg\"\r\n  ],\r\n  \"bgOpacity\": [\r\n    0.2\r\n  ],\r\n  \"bgBlur\": [\r\n    10\r\n  ],\r\n  \"bgParallaxIntensity\": [\r\n    0.1\r\n  ],\r\n  \"performanceMode\": [\r\n    true\r\n  ],\r\n  \"overrideColorPalette\": true,\r\n  \"colorPalette\": [\r\n    [\r\n      5,\r\n      128,\r\n      177\r\n    ],\r\n    [\r\n      220,\r\n      215,\r\n      218\r\n    ],\r\n    [\r\n      21,\r\n      13,\r\n      52\r\n    ],\r\n    [\r\n      93,\r\n      197,\r\n      225\r\n    ],\r\n    [\r\n      54,\r\n      31,\r\n      98\r\n    ]\r\n  ],\r\n  \"againstToEdge\": [\r\n    0\r\n  ],\r\n  \"fitWithEdge\": [\r\n    0\r\n  ],\r\n  \"reverseMove\": [\r\n    false\r\n  ],\r\n  \"autoOpacityBlur\": [\r\n    true\r\n  ]\r\n}", "updateAlertVersion": "1", "lastColorVars": "\n                    :root {\n                        --pa: rgba(5, 128, 177, 0.75);\n                        --pat: #dddddd;\n                        --p: rgb(5, 128, 177);\n                        --pt: #dddddd;\n                        \n                        --sa: rgba(220, 215, 218, 0.75);\n                        --sat: #1a1a1a;\n                        --s: rgb(220, 215, 218);\n                        --st: #1a1a1a;\n                        \n                        --ba: rgba(21, 13, 52, 0.75);\n                        --bat: #dddddd;\n                        --b: rgb(21, 13, 52);\n                        --bt: #dddddd;\n                        \n--c0: rgb(5, 128, 177);\n--c0t: rgb(221, 221, 221);\n--c0a5: rgba(5, 128, 177, 0.05);\n--c0a10: rgba(5, 128, 177, 0.1);\n--c0a15: rgba(5, 128, 177, 0.15);\n--c0a20: rgba(5, 128, 177, 0.2);\n--c0a25: rgba(5, 128, 177, 0.25);\n--c0a30: rgba(5, 128, 177, 0.3);\n--c0a35: rgba(5, 128, 177, 0.35);\n--c0a40: rgba(5, 128, 177, 0.4);\n--c0a45: rgba(5, 128, 177, 0.45);\n--c0a50: rgba(5, 128, 177, 0.5);\n--c0a55: rgba(5, 128, 177, 0.55);\n--c0a60: rgba(5, 128, 177, 0.6);\n--c0a65: rgba(5, 128, 177, 0.65);\n--c0a70: rgba(5, 128, 177, 0.7);\n--c0a75: rgba(5, 128, 177, 0.75);\n--c0a80: rgba(5, 128, 177, 0.8);\n--c0a85: rgba(5, 128, 177, 0.85);\n--c0a90: rgba(5, 128, 177, 0.9);\n--c0a95: rgba(5, 128, 177, 0.95);\n--c1: rgb(220, 215, 218);\n--c1t: rgb(26, 26, 26);\n--c1a5: rgba(220, 215, 218, 0.05);\n--c1a10: rgba(220, 215, 218, 0.1);\n--c1a15: rgba(220, 215, 218, 0.15);\n--c1a20: rgba(220, 215, 218, 0.2);\n--c1a25: rgba(220, 215, 218, 0.25);\n--c1a30: rgba(220, 215, 218, 0.3);\n--c1a35: rgba(220, 215, 218, 0.35);\n--c1a40: rgba(220, 215, 218, 0.4);\n--c1a45: rgba(220, 215, 218, 0.45);\n--c1a50: rgba(220, 215, 218, 0.5);\n--c1a55: rgba(220, 215, 218, 0.55);\n--c1a60: rgba(220, 215, 218, 0.6);\n--c1a65: rgba(220, 215, 218, 0.65);\n--c1a70: rgba(220, 215, 218, 0.7);\n--c1a75: rgba(220, 215, 218, 0.75);\n--c1a80: rgba(220, 215, 218, 0.8);\n--c1a85: rgba(220, 215, 218, 0.85);\n--c1a90: rgba(220, 215, 218, 0.9);\n--c1a95: rgba(220, 215, 218, 0.95);\n--c2: rgb(21, 13, 52);\n--c2t: rgb(221, 221, 221);\n--c2a5: rgba(21, 13, 52, 0.05);\n--c2a10: rgba(21, 13, 52, 0.1);\n--c2a15: rgba(21, 13, 52, 0.15);\n--c2a20: rgba(21, 13, 52, 0.2);\n--c2a25: rgba(21, 13, 52, 0.25);\n--c2a30: rgba(21, 13, 52, 0.3);\n--c2a35: rgba(21, 13, 52, 0.35);\n--c2a40: rgba(21, 13, 52, 0.4);\n--c2a45: rgba(21, 13, 52, 0.45);\n--c2a50: rgba(21, 13, 52, 0.5);\n--c2a55: rgba(21, 13, 52, 0.55);\n--c2a60: rgba(21, 13, 52, 0.6);\n--c2a65: rgba(21, 13, 52, 0.65);\n--c2a70: rgba(21, 13, 52, 0.7);\n--c2a75: rgba(21, 13, 52, 0.75);\n--c2a80: rgba(21, 13, 52, 0.8);\n--c2a85: rgba(21, 13, 52, 0.85);\n--c2a90: rgba(21, 13, 52, 0.9);\n--c2a95: rgba(21, 13, 52, 0.95);\n--c3: rgb(93, 197, 225);\n--c3t: rgb(26, 26, 26);\n--c3a5: rgba(93, 197, 225, 0.05);\n--c3a10: rgba(93, 197, 225, 0.1);\n--c3a15: rgba(93, 197, 225, 0.15);\n--c3a20: rgba(93, 197, 225, 0.2);\n--c3a25: rgba(93, 197, 225, 0.25);\n--c3a30: rgba(93, 197, 225, 0.3);\n--c3a35: rgba(93, 197, 225, 0.35);\n--c3a40: rgba(93, 197, 225, 0.4);\n--c3a45: rgba(93, 197, 225, 0.45);\n--c3a50: rgba(93, 197, 225, 0.5);\n--c3a55: rgba(93, 197, 225, 0.55);\n--c3a60: rgba(93, 197, 225, 0.6);\n--c3a65: rgba(93, 197, 225, 0.65);\n--c3a70: rgba(93, 197, 225, 0.7);\n--c3a75: rgba(93, 197, 225, 0.75);\n--c3a80: rgba(93, 197, 225, 0.8);\n--c3a85: rgba(93, 197, 225, 0.85);\n--c3a90: rgba(93, 197, 225, 0.9);\n--c3a95: rgba(93, 197, 225, 0.95);\n--c4: rgb(54, 31, 98);\n--c4t: rgb(221, 221, 221);\n--c4a5: rgba(54, 31, 98, 0.05);\n--c4a10: rgba(54, 31, 98, 0.1);\n--c4a15: rgba(54, 31, 98, 0.15);\n--c4a20: rgba(54, 31, 98, 0.2);\n--c4a25: rgba(54, 31, 98, 0.25);\n--c4a30: rgba(54, 31, 98, 0.3);\n--c4a35: rgba(54, 31, 98, 0.35);\n--c4a40: rgba(54, 31, 98, 0.4);\n--c4a45: rgba(54, 31, 98, 0.45);\n--c4a50: rgba(54, 31, 98, 0.5);\n--c4a55: rgba(54, 31, 98, 0.55);\n--c4a60: rgba(54, 31, 98, 0.6);\n--c4a65: rgba(54, 31, 98, 0.65);\n--c4a70: rgba(54, 31, 98, 0.7);\n--c4a75: rgba(54, 31, 98, 0.75);\n--c4a80: rgba(54, 31, 98, 0.8);\n--c4a85: rgba(54, 31, 98, 0.85);\n--c4a90: rgba(54, 31, 98, 0.9);\n--c4a95: rgba(54, 31, 98, 0.95);\n                    }\n                ", "lastPath": "D:\\Downloads\\SR-v2.7-pc\\SR.exe", "lastLaunchKey": "b231756b65368dca5ce7df227229ddfa", "renpyFontSizeOff": "0", "AutoSaveDisabled_renpy4": "0", "AutoSaveInv_renpy4": "180000", "engOpt": "0", "engineSelVal": "<PERSON>", "lastTo": "14", "lastBackupGameSaveInvTime": "0", "AutoSaveDisabled_mv": "0", "AutoSaveInv_mv": "180000", "disableAutoLoadTrsDataOnce": "0", "AutoSaveDisabled_mz": "0", "AutoSaveInv_mz": "180000"}