{"name": "iso-8859-4", "version": "0.1.2", "description": "A robust iso-8859-4 encoder/decoder written in JavaScript.", "homepage": "http://mths.be/iso-8859-4", "main": "iso-8859-4.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "csisolatin4", "iso-8859-4", "iso-ir-110", "iso8859-4", "iso88594", "iso_8859-4", "iso_8859-4:1988", "l4", "latin4"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/iso-8859-4.git"}, "bugs": {"url": "https://github.com/mathiasbynens/iso-8859-4/issues"}, "files": ["LICENSE-MIT.txt", "iso-8859-4.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}