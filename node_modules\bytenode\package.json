{"name": "bytenode", "version": "1.5.7", "description": "A minimalist bytecode compiler for Node.js", "main": "lib/index.js", "bin": "lib/cli.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"test": "mocha ./test/*.test.js"}, "repository": {"type": "git", "url": "git+https://github.com/bytenode/bytenode.git"}, "keywords": ["bytenode", "bytecode", "v8-snapshots", "compile", "compiler"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/bytenode/bytenode/issues"}, "homepage": "https://github.com/bytenode/bytenode#readme", "devDependencies": {"electron": "^34.1.1", "mocha": "^11.1.0"}}