/*! http://mths.be/iso-8859-15 v0.1.2 by @mathias | MIT license */
;(function(root) {

	// Detect free variables `exports`.
	var freeExports = typeof exports == 'object' && exports;

	// Detect free variable `module`.
	var freeModule = typeof module == 'object' && module &&
		module.exports == freeExports && module;

	// Detect free variable `global`, from Node.js or Browserified code, and use
	// it as `root`.
	var freeGlobal = typeof global == 'object' && global;
	if (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal) {
		root = freeGlobal;
	}

	/*--------------------------------------------------------------------------*/

	var object = {};
	var hasOwnProperty = object.hasOwnProperty;
	var stringFromCharCode = String.fromCharCode;

	var INDEX_BY_CODE_POINT = {'128':0,'129':1,'130':2,'131':3,'132':4,'133':5,'134':6,'135':7,'136':8,'137':9,'138':10,'139':11,'140':12,'141':13,'142':14,'143':15,'144':16,'145':17,'146':18,'147':19,'148':20,'149':21,'150':22,'151':23,'152':24,'153':25,'154':26,'155':27,'156':28,'157':29,'158':30,'159':31,'160':32,'161':33,'162':34,'163':35,'165':37,'167':39,'169':41,'170':42,'171':43,'172':44,'173':45,'174':46,'175':47,'176':48,'177':49,'178':50,'179':51,'181':53,'182':54,'183':55,'185':57,'186':58,'187':59,'191':63,'192':64,'193':65,'194':66,'195':67,'196':68,'197':69,'198':70,'199':71,'200':72,'201':73,'202':74,'203':75,'204':76,'205':77,'206':78,'207':79,'208':80,'209':81,'210':82,'211':83,'212':84,'213':85,'214':86,'215':87,'216':88,'217':89,'218':90,'219':91,'220':92,'221':93,'222':94,'223':95,'224':96,'225':97,'226':98,'227':99,'228':100,'229':101,'230':102,'231':103,'232':104,'233':105,'234':106,'235':107,'236':108,'237':109,'238':110,'239':111,'240':112,'241':113,'242':114,'243':115,'244':116,'245':117,'246':118,'247':119,'248':120,'249':121,'250':122,'251':123,'252':124,'253':125,'254':126,'255':127,'338':60,'339':61,'352':38,'353':40,'376':62,'381':52,'382':56,'8364':36};
	var INDEX_BY_POINTER = {'0':'\x80','1':'\x81','2':'\x82','3':'\x83','4':'\x84','5':'\x85','6':'\x86','7':'\x87','8':'\x88','9':'\x89','10':'\x8A','11':'\x8B','12':'\x8C','13':'\x8D','14':'\x8E','15':'\x8F','16':'\x90','17':'\x91','18':'\x92','19':'\x93','20':'\x94','21':'\x95','22':'\x96','23':'\x97','24':'\x98','25':'\x99','26':'\x9A','27':'\x9B','28':'\x9C','29':'\x9D','30':'\x9E','31':'\x9F','32':'\xA0','33':'\xA1','34':'\xA2','35':'\xA3','36':'\u20AC','37':'\xA5','38':'\u0160','39':'\xA7','40':'\u0161','41':'\xA9','42':'\xAA','43':'\xAB','44':'\xAC','45':'\xAD','46':'\xAE','47':'\xAF','48':'\xB0','49':'\xB1','50':'\xB2','51':'\xB3','52':'\u017D','53':'\xB5','54':'\xB6','55':'\xB7','56':'\u017E','57':'\xB9','58':'\xBA','59':'\xBB','60':'\u0152','61':'\u0153','62':'\u0178','63':'\xBF','64':'\xC0','65':'\xC1','66':'\xC2','67':'\xC3','68':'\xC4','69':'\xC5','70':'\xC6','71':'\xC7','72':'\xC8','73':'\xC9','74':'\xCA','75':'\xCB','76':'\xCC','77':'\xCD','78':'\xCE','79':'\xCF','80':'\xD0','81':'\xD1','82':'\xD2','83':'\xD3','84':'\xD4','85':'\xD5','86':'\xD6','87':'\xD7','88':'\xD8','89':'\xD9','90':'\xDA','91':'\xDB','92':'\xDC','93':'\xDD','94':'\xDE','95':'\xDF','96':'\xE0','97':'\xE1','98':'\xE2','99':'\xE3','100':'\xE4','101':'\xE5','102':'\xE6','103':'\xE7','104':'\xE8','105':'\xE9','106':'\xEA','107':'\xEB','108':'\xEC','109':'\xED','110':'\xEE','111':'\xEF','112':'\xF0','113':'\xF1','114':'\xF2','115':'\xF3','116':'\xF4','117':'\xF5','118':'\xF6','119':'\xF7','120':'\xF8','121':'\xF9','122':'\xFA','123':'\xFB','124':'\xFC','125':'\xFD','126':'\xFE','127':'\xFF'};

	// http://encoding.spec.whatwg.org/#error-mode
	var error = function(codePoint, mode) {
		if (mode == 'replacement') {
			return '\uFFFD';
		}
		if (codePoint != null && mode == 'html') {
			return '&#' + codePoint + ';';
		}
		// Else, `mode == 'fatal'`.
		throw Error();
	};

	// http://encoding.spec.whatwg.org/#single-byte-decoder
	var decode = function(input, options) {
		var mode;
		if (options && options.mode) {
			mode = options.mode.toLowerCase();
		}
		// “An error mode […] is either `replacement` (default) or `fatal` for a
		// decoder.”
		if (mode != 'replacement' && mode != 'fatal') {
			mode = 'replacement';
		}
		var length = input.length;
		var index = -1;
		var byteValue;
		var pointer;
		var result = '';
		while (++index < length) {
			byteValue = input.charCodeAt(index);
			// “If `byte` is in the range `0x00` to `0x7F`, return a code point whose
			// value is `byte`.”
			if (byteValue >= 0x00 && byteValue <= 0x7F) {
				result += stringFromCharCode(byteValue);
				continue;
			}
			// “Let `code point` be the index code point for `byte − 0x80` in index
			// `single-byte`.”
			pointer = byteValue - 0x80;
			if (hasOwnProperty.call(INDEX_BY_POINTER, pointer)) {
				// “Return a code point whose value is `code point`.”
				result += INDEX_BY_POINTER[pointer];
			} else {
				// “If `code point` is `null`, return `error`.”
				result += error(null, mode);
			}
		}
		return result;
	};

	// http://encoding.spec.whatwg.org/#single-byte-encoder
	var encode = function(input, options) {
		var mode;
		if (options && options.mode) {
			mode = options.mode.toLowerCase();
		}
		// “An error mode […] is either `fatal` (default) or `HTML` for an
		// encoder.”
		if (mode != 'fatal' && mode != 'html') {
			mode = 'fatal';
		}
		var length = input.length;
		var index = -1;
		var codePoint;
		var pointer;
		var result = '';
		while (++index < length) {
			codePoint = input.charCodeAt(index);
			// “If `code point` is in the range U+0000 to U+007F, return a byte whose
			// value is `code point`.”
			if (codePoint >= 0x00 && codePoint <= 0x7F) {
				result += stringFromCharCode(codePoint);
				continue;
			}
			// “Let `pointer` be the index pointer for `code point` in index
			// `single-byte`.”
			if (hasOwnProperty.call(INDEX_BY_CODE_POINT, codePoint)) {
				pointer = INDEX_BY_CODE_POINT[codePoint];
				// “Return a byte whose value is `pointer + 0x80`.”
				result += stringFromCharCode(pointer + 0x80);
			} else {
				// “If `pointer` is `null`, return `error` with `code point`.”
				result += error(codePoint, mode);
			}
		}
		return result;
	};

	var iso885915 = {
		'encode': encode,
		'decode': decode,
		'labels': [
			'csisolatin9',
			'iso-8859-15',
			'iso8859-15',
			'iso885915',
			'iso_8859-15',
			'l9'
		],
		'version': '0.1.2'
	};

	// Some AMD build optimizers, like r.js, check for specific condition patterns
	// like the following:
	if (
		typeof define == 'function' &&
		typeof define.amd == 'object' &&
		define.amd
	) {
		define(function() {
			return iso885915;
		});
	}	else if (freeExports && !freeExports.nodeType) {
		if (freeModule) { // in Node.js or RingoJS v0.8.0+
			freeModule.exports = iso885915;
		} else { // in Narwhal or RingoJS v0.7.0-
			for (var key in iso885915) {
				iso885915.hasOwnProperty(key) && (freeExports[key] = iso885915[key]);
			}
		}
	} else { // in Rhino or a web browser
		root.iso885915 = iso885915;
	}

}(this));
