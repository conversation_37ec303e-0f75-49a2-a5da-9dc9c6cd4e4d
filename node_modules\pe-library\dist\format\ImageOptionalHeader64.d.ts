import FormatBase from './FormatBase';
export default class ImageOptionalHeader64 extends FormatBase {
    static readonly size = 112;
    static readonly DEFAULT_MAGIC = 523;
    private constructor();
    static from(bin: ArrayBuffer, offset?: number): ImageOptionalHeader64;
    get magic(): number;
    set magic(val: number);
    get majorLinkerVersion(): number;
    set majorLinkerVersion(val: number);
    get minorLinkerVersion(): number;
    set minorLinkerVersion(val: number);
    get sizeOfCode(): number;
    set sizeOfCode(val: number);
    get sizeOfInitializedData(): number;
    set sizeOfInitializedData(val: number);
    get sizeOfUninitializedData(): number;
    set sizeOfUninitializedData(val: number);
    get addressOfEntryPoint(): number;
    set addressOfEntryPoint(val: number);
    get baseOfCode(): number;
    set baseOfCode(val: number);
    get imageBase(): number;
    set imageBase(val: number);
    get imageBaseBigInt(): bigint;
    set imageBaseBigInt(val: bigint);
    get sectionAlignment(): number;
    set sectionAlignment(val: number);
    get fileAlignment(): number;
    set fileAlignment(val: number);
    get majorOperatingSystemVersion(): number;
    set majorOperatingSystemVersion(val: number);
    get minorOperatingSystemVersion(): number;
    set minorOperatingSystemVersion(val: number);
    get majorImageVersion(): number;
    set majorImageVersion(val: number);
    get minorImageVersion(): number;
    set minorImageVersion(val: number);
    get majorSubsystemVersion(): number;
    set majorSubsystemVersion(val: number);
    get minorSubsystemVersion(): number;
    set minorSubsystemVersion(val: number);
    get win32VersionValue(): number;
    set win32VersionValue(val: number);
    get sizeOfImage(): number;
    set sizeOfImage(val: number);
    get sizeOfHeaders(): number;
    set sizeOfHeaders(val: number);
    get checkSum(): number;
    set checkSum(val: number);
    get subsystem(): number;
    set subsystem(val: number);
    get dllCharacteristics(): number;
    set dllCharacteristics(val: number);
    get sizeOfStackReserve(): number;
    set sizeOfStackReserve(val: number);
    get sizeOfStackReserveBigInt(): bigint;
    set sizeOfStackReserveBigInt(val: bigint);
    get sizeOfStackCommit(): number;
    set sizeOfStackCommit(val: number);
    get sizeOfStackCommitBigInt(): bigint;
    set sizeOfStackCommitBigInt(val: bigint);
    get sizeOfHeapReserve(): number;
    set sizeOfHeapReserve(val: number);
    get sizeOfHeapReserveBigInt(): bigint;
    set sizeOfHeapReserveBigInt(val: bigint);
    get sizeOfHeapCommit(): number;
    set sizeOfHeapCommit(val: number);
    get sizeOfHeapCommitBigInt(): bigint;
    set sizeOfHeapCommitBigInt(val: bigint);
    get loaderFlags(): number;
    set loaderFlags(val: number);
    get numberOfRvaAndSizes(): number;
    set numberOfRvaAndSizes(val: number);
}
