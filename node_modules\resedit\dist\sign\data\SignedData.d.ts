import DigestAlgorithmIdentifier from './AlgorithmIdentifier';
import ContentInfo from './ContentInfo';
import DERObject from './DERObject';
export default class SignedData implements DERObject {
    version: number;
    digestAlgorithms: DigestAlgorithmIdentifier[];
    contentInfo: ContentInfo;
    signerInfos: DERObject[];
    certificates?: DERObject[] | undefined;
    crls?: DERObject[] | undefined;
    constructor(version: number, digestAlgorithms: DigestAlgorithmIdentifier[], contentInfo: ContentInfo, signerInfos: DERObject[], certificates?: DERObject[] | undefined, crls?: DERObject[] | undefined);
    toDER(): number[];
}
