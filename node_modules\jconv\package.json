{"name": "jconv", "description": "Pure JavaScript Iconv for Japanese encodings. (Shift_JIS, ISO-2022-JP, EUC-JP, UTF-8, UCS-2)", "version": "0.1.5", "auther": "narirou <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/narirou/jconv.git"}, "main": "jconv.js", "scripts": {"test": "grunt test"}, "engines": {"node": ">=0.8.0"}, "keywords": ["iconv", "encode", "encoding", "charset", "japanese"], "devDependencies": {"async": "*", "iconv": "*", "benchmark": "*", "should": "*", "grunt": "~0.4.2", "grunt-closurecompiler": "*", "grunt-mocha-test": "*"}, "readmeFilename": "README.md", "license": "MIT"}