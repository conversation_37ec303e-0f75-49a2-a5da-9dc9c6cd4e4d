import ArrayFormatBase from './ArrayFormatBase';
import FormatBase from './FormatBase';
import ImageDataDirectoryArray, { ImageDataDirectory } from './ImageDataDirectoryArray';
import ImageDirectoryEntry from './ImageDirectoryEntry';
import ImageDosHeader from './ImageDosHeader';
import ImageFileHeader from './ImageFileHeader';
import ImageNtHeaders from './ImageNtHeaders';
import ImageOptionalHeader from './ImageOptionalHeader';
import ImageOptionalHeader64 from './ImageOptionalHeader64';
import ImageSectionHeaderArray, { ImageSectionHeader } from './ImageSectionHeaderArray';
export { ArrayFormatBase, FormatBase, ImageDataDirectory, ImageDataDirectoryArray, ImageDirectoryEntry, ImageDosHeader, ImageFileHeader, ImageNtHeaders, ImageOptionalHeader, ImageOptionalHeader64, ImageSectionHeader, ImageSectionHeaderArray, };
export declare function getImageDosHeader(bin: ArrayBuffer): ImageDosHeader;
export declare function getImageNtHeadersByDosHeader(bin: ArrayBuffer, dosHeader: ImageDosHeader): ImageNtHeaders;
export declare function getImageSectionHeadersByNtHeaders(bin: ArrayBuffer, dosHeader: ImageDosHeader, ntHeaders: ImageNtHeaders): ImageSectionHeaderArray;
export declare function findImageSectionBlockByDirectoryEntry(bin: ArrayBuffer, dosHeader: ImageDosHeader, ntHeaders: ImageNtHeaders, entryType: ImageDirectoryEntry): ArrayBuffer | null;
