{"injectAtOEP": true, "makeLaunchBat": true, "lazyInject": false, "injectOnTheFly": false, "packageRelease": false, "selfNWStart": false, "largeAddressAware": false, "argAppend": "", "constArgs": {"gameExe": "C:\\Users\\<USER>\\Saved Games\\ProjectMyriamLifeandExplorations-ch5.12+p-pc\\ProjectMyriamLifeandExplorations.exe", "dllPath": "loaders\\PythonHook64.dll", "is64Bit": true, "needEnglishPath": true, "needEnglishExe": false, "engPathRegxp": null, "envAppend": {}}, "libConf": {"path": "C:\\Users\\<USER>\\Saved Games\\ProjectMyriamLifeandExplorations-ch5.12+p-pc\\ProjectMyriamLifeandExplorations.exe", "libConfKey": "4ea8070ccc767e9cc863001f8368be18", "added": 1751791431829, "lastLaunchInLib": 1751791431829, "engine": "", "title": "Project Myriam Life and Explorations", "titleTrs": "<PERSON><PERSON> <PERSON>n <PERSON>m Life and Explorations", "titleUser": "", "icon": "", "pic": "../gameLib/4ea8070ccc767e9cc863001f8368be18_pic", "picFirstSearch": true, "tags": []}}