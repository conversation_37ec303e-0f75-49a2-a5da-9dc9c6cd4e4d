{"name": "koi8-r", "version": "0.1.2", "description": "A robust koi8-r encoder/decoder written in JavaScript.", "homepage": "http://mths.be/koi8-r", "main": "koi8-r.js", "keywords": ["codec", "decoder", "decoding", "encoder", "encoding", "legacy", "legacy-encoding", "cskoi8r", "koi", "koi8", "koi8-r", "koi8_r"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/koi8-r.git"}, "bugs": {"url": "https://github.com/mathiasbynens/koi8-r/issues"}, "files": ["LICENSE-MIT.txt", "koi8-r.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "jsesc": "~0.4.3", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11", "string.fromcodepoint": "~0.2.0"}}