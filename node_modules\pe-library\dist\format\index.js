"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findImageSectionBlockByDirectoryEntry = exports.getImageSectionHeadersByNtHeaders = exports.getImageNtHeadersByDosHeader = exports.getImageDosHeader = exports.ImageSectionHeaderArray = exports.ImageOptionalHeader64 = exports.ImageOptionalHeader = exports.ImageNtHeaders = exports.ImageFileHeader = exports.ImageDosHeader = exports.ImageDirectoryEntry = exports.ImageDataDirectoryArray = exports.FormatBase = exports.ArrayFormatBase = void 0;
var ArrayFormatBase_1 = require("./ArrayFormatBase");
exports.ArrayFormatBase = ArrayFormatBase_1.default;
var FormatBase_1 = require("./FormatBase");
exports.FormatBase = FormatBase_1.default;
var ImageDataDirectoryArray_1 = require("./ImageDataDirectoryArray");
exports.ImageDataDirectoryArray = ImageDataDirectoryArray_1.default;
var ImageDirectoryEntry_1 = require("./ImageDirectoryEntry");
exports.ImageDirectoryEntry = ImageDirectoryEntry_1.default;
var ImageDosHeader_1 = require("./ImageDosHeader");
exports.ImageDosHeader = ImageDosHeader_1.default;
var ImageFileHeader_1 = require("./ImageFileHeader");
exports.ImageFileHeader = ImageFileHeader_1.default;
var ImageNtHeaders_1 = require("./ImageNtHeaders");
exports.ImageNtHeaders = ImageNtHeaders_1.default;
var ImageOptionalHeader_1 = require("./ImageOptionalHeader");
exports.ImageOptionalHeader = ImageOptionalHeader_1.default;
var ImageOptionalHeader64_1 = require("./ImageOptionalHeader64");
exports.ImageOptionalHeader64 = ImageOptionalHeader64_1.default;
var ImageSectionHeaderArray_1 = require("./ImageSectionHeaderArray");
exports.ImageSectionHeaderArray = ImageSectionHeaderArray_1.default;
function getImageDosHeader(bin) {
    return ImageDosHeader_1.default.from(bin);
}
exports.getImageDosHeader = getImageDosHeader;
function getImageNtHeadersByDosHeader(bin, dosHeader) {
    return ImageNtHeaders_1.default.from(bin, dosHeader.newHeaderAddress);
}
exports.getImageNtHeadersByDosHeader = getImageNtHeadersByDosHeader;
function getImageSectionHeadersByNtHeaders(bin, dosHeader, ntHeaders) {
    return ImageSectionHeaderArray_1.default.from(bin, ntHeaders.fileHeader.numberOfSections, dosHeader.newHeaderAddress + ntHeaders.byteLength);
}
exports.getImageSectionHeadersByNtHeaders = getImageSectionHeadersByNtHeaders;
function findImageSectionBlockByDirectoryEntry(bin, dosHeader, ntHeaders, entryType) {
    var arr = ImageSectionHeaderArray_1.default.from(bin, ntHeaders.fileHeader.numberOfSections, dosHeader.newHeaderAddress + ntHeaders.byteLength);
    var len = arr.length;
    var rva = ntHeaders.optionalHeaderDataDirectory.get(entryType).virtualAddress;
    for (var i = 0; i < len; ++i) {
        var sec = arr.get(i);
        var vaEnd = sec.virtualAddress + sec.virtualSize;
        if (rva >= sec.virtualAddress && rva < vaEnd) {
            var ptr = sec.pointerToRawData;
            if (!ptr) {
                return null;
            }
            return bin.slice(ptr, ptr + sec.sizeOfRawData);
        }
        if (rva < sec.virtualAddress) {
            return null;
        }
    }
    return null;
}
exports.findImageSectionBlockByDirectoryEntry = findImageSectionBlockByDirectoryEntry;
