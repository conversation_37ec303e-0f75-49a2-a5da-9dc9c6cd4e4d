{"injectAtOEP": true, "makeLaunchBat": true, "lazyInject": false, "injectOnTheFly": false, "packageRelease": false, "selfNWStart": false, "largeAddressAware": false, "argAppend": "", "constArgs": {"gameExe": "C:\\Users\\<USER>\\Downloads\\ProjectMyriamLifeandExplorations-ch5.12+p-pc\\ProjectMyriamLifeandExplorations.exe", "dllPath": "loaders\\PythonHook64.dll", "is64Bit": true, "needEnglishPath": true, "needEnglishExe": false, "engPathRegxp": null, "envAppend": {}}, "libConf": {"path": "C:\\Users\\<USER>\\Downloads\\ProjectMyriamLifeandExplorations-ch5.12+p-pc\\ProjectMyriamLifeandExplorations.exe", "libConfKey": "c30c5312723f7a8c0e183b4d203119f1", "added": 1750442324171, "lastLaunchInLib": 1750442324171, "engine": "", "title": "Project Myriam Life and Explorations", "titleTrs": "<PERSON><PERSON> <PERSON>n <PERSON>m Life and Explorations", "titleUser": "", "icon": "", "pic": "../gameLib/c30c5312723f7a8c0e183b4d203119f1_pic", "picFirstSearch": true, "tags": []}}