{"injectAtOEP": true, "makeLaunchBat": true, "lazyInject": false, "injectOnTheFly": false, "packageRelease": false, "selfNWStart": false, "largeAddressAware": false, "argAppend": "", "constArgs": {"gameExe": "C:\\Users\\<USER>\\Downloads\\Babysitter-0.2.2b.-win\\Babysitter.exe", "dllPath": "loaders\\PythonHook.dll", "is64Bit": false, "needEnglishPath": true, "needEnglishExe": false, "engPathRegxp": null, "usingMockToolPath": true, "envAppend": {}}, "libConf": {"path": "C:\\Users\\<USER>\\Downloads\\Babysitter-0.2.2b.-win\\Babysitter.exe", "libConfKey": "bb358725029a5dbf02a2584584b77cff", "added": 1752216927363, "lastLaunchInLib": 1752216927363, "engine": "", "title": "Babysitter", "titleTrs": "Babysitter", "titleUser": "", "icon": "", "pic": "../gameLib/bb358725029a5dbf02a2584584b77cff_pic", "picFirstSearch": true, "tags": []}}