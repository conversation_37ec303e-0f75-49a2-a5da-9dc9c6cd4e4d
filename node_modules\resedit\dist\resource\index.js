"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionInfo = exports.VersionFileType = exports.VersionFileFontSubtype = exports.VersionFileDriverSubtype = exports.VersionFileOS = exports.VersionFileFlags = exports.StringTable = exports.IconGroupEntry = void 0;
var IconGroupEntry_1 = require("./IconGroupEntry");
exports.IconGroupEntry = IconGroupEntry_1.default;
var StringTable_1 = require("./StringTable");
exports.StringTable = StringTable_1.default;
var VersionFileFlags_1 = require("./VersionFileFlags");
exports.VersionFileFlags = VersionFileFlags_1.default;
var VersionFileOS_1 = require("./VersionFileOS");
exports.VersionFileOS = VersionFileOS_1.default;
var VersionFileSubtypes_1 = require("./VersionFileSubtypes");
Object.defineProperty(exports, "VersionFileDriverSubtype", { enumerable: true, get: function () { return VersionFileSubtypes_1.VersionFileDriverSubtype; } });
Object.defineProperty(exports, "VersionFileFontSubtype", { enumerable: true, get: function () { return VersionFileSubtypes_1.VersionFileFontSubtype; } });
var VersionFileType_1 = require("./VersionFileType");
exports.VersionFileType = VersionFileType_1.default;
var VersionInfo_1 = require("./VersionInfo");
exports.VersionInfo = VersionInfo_1.default;
