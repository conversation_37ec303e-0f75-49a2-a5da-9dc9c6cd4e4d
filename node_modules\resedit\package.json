{"name": "resedit", "version": "1.6.0", "engines": {"node": ">=12", "npm": ">=6"}, "engineStrict": true, "description": "Node.js library editing Windows Resource data", "main": "./dist/index.js", "module": "./dist/_esm/index.js", "types": "./dist/index.d.ts", "author": "jet", "license": "MIT", "homepage": "https://github.com/jet2jet/resedit-js", "keywords": ["windows", "resource", "javascript", "library", "version", "icon", "edit", "resedit", "pe-executable", "authenticode"], "repository": {"type": "git", "url": "https://github.com/jet2jet/resedit-js.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc -p ./tsconfig.app.json", "build:esm": "tsc -p ./tsconfig.app.esm.json", "lint": "npm run lint:prettier && npm run lint:eslint", "lint:eslint": "eslint -c .eslintrc.yml --ext .js,.jsx,.ts,.tsx .", "lint:eslint:fix": "eslint -c .eslintrc.yml --fix --ext .js,.jsx,.ts,.tsx .", "lint:fix": "npm run lint:prettier:fix && npm run lint:eslint:fix", "lint:prettier": "prettier --config ./.prettierrc.yml --check \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "lint:prettier:fix": "prettier --config ./.prettierrc.yml --write \"**/*.{js,jsx,ts,tsx,yml,json,md}\"", "test": "jest --config ./jest.config.basic.js", "test:basic": "jest --config ./jest.config.basic.js", "test:lint": "npm run lint", "test:win-x86": "jest --config ./jest.config.win-x86.js", "test:win-x64": "jest --config ./jest.config.win-x64.js", "version": "node ./tools/updateVersion.js ./src/main/version.ts && git add -A ./src/main/version.ts"}, "dependencies": {"pe-library": "^0.3.0"}, "devDependencies": {"@types/jest": "^27.5.0", "@types/node": "^12.20.37", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "eslint": "^7.24.0", "eslint-config-prettier": "^8.2.0", "eslint-config-standard": "^16.0.2", "eslint-config-standard-with-typescript": "^20.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "jest": "^28.0.3", "prettier": "^2.2.1", "ts-jest": "^28.0.1", "typescript": "~4.2.4"}}