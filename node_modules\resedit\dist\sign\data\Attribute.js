"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var derUtil_1 = require("./derUtil");
var Attribute = /** @class */ (function () {
    function Attribute(attrType, attrValues) {
        this.attrType = attrType;
        this.attrValues = attrValues;
    }
    Attribute.prototype.toDER = function () {
        return derUtil_1.makeDERSequence(this.attrType.toDER().concat(derUtil_1.arrayToDERSet(this.attrValues)));
    };
    return Attribute;
}());
exports.default = Attribute;
